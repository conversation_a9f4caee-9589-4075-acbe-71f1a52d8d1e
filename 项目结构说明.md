# Tx 交易平台项目结构说明

## 项目概述
Tx 是一个基于 kdb+ 的策略交易平台，专门为中国股票和期货市场量身定制。该平台提供完整的交易基础设施，包括数据接入、策略执行、风险管理和用户界面等功能。

## 目录结构

### 核心模块 (core/)
- **base.q** - 核心基础模块，定义了系统的基本数据结构和初始化逻辑
- **api.q** - API接口定义
- **p2p.q** - 点对点通信模块
- **enum.q** - 枚举定义
- **dotz.q** - 系统工具函数
- **habase.q** - 高可用性基础模块
- **fcbase.q** - 期货交易核心模块
- **ftbase.q** - 期货交易基础模块
- **fqbase.q** - 期货行情基础模块
- **febase.q** - 期货执行基础模块
- **fubase.q** - 期货用户基础模块
- **fabase.q** - 期货算法基础模块
- **fpbase.q** - 期货转发基础模块
- **rcbase.q** - 风控基础模块
- **skiptp.q** - 跳跃点处理模块
- **tick.q** - 行情数据处理模块

### 数据接入层 (feed/)
支持多种数据源和交易接口：
- **ctp/** - CTP接口（期货主流接口）
- **xtp/** - XTP接口（证券交易）
- **fix/** - FIX协议接口
- **tdf/** - 通达信数据接口
- **atp/** - ATP接口
- **jg/** - 金股接口
- **tws/** - TWS接口
- **ufx/** - UFX接口
- **hsh5/** - 恒生H5接口
- **hsldp/** - 恒生LDP接口
- **hsnsq/** - 恒生NSQ接口
- **xsheg/** - 上交所接口
- **rootnet/** - 根网接口
- **zhongchang/** - 中昌接口
- **web/** - Web接口
- **file/** - 文件数据源
- **backtest/** - 回测数据源
- **bar/** - K线数据处理
- **l2hr/** - Level2高频数据
- **socket.q** - 通用Socket通信

### 策略库 (tsl/)
- **tslib.q** - 策略库核心模块
- **grid.q** - 网格策略
- **grid_ex.q** - 扩展网格策略
- **algo/** - 算法策略目录
  - **algobase.q** - 算法基础模块
  - **algolib.q** - 算法库
  - **algofuex.q** - 期货算法执行

### 用户界面 (ui/)
- **uibase.q** - UI基础模块
- **js/** - JavaScript前端文件
  - **fcui.js** - 期货交易界面
  - **ftui.js** - 期货交易UI
  - **txlib.js** - 交易库JS

### 扩展模块 (ext/)
- **src/** - C/C++源代码
  - 各种交易接口的C++实现
  - **k.h**, **kcomm.h** - kdb+接口头文件
  - **buildl64.sh** - Linux 64位编译脚本
- **dll/** - 编译后的动态链接库

### 配置文件 (conf/)
- **qtx.eg/** - 配置示例目录
- **cfftbase.q** - 期货交易基础配置
- **cffubase.q** - 期货用户基础配置
- **exac.q** - 执行账户配置
- **holiday.q** - 节假日配置

### 工具库 (lib/)
- **handy.q** - 便利工具函数
- **extutil.q** - 扩展工具
- **math.q** - 数学函数库
- **mathex.q** - 扩展数学函数
- **opt.q** - 期权相关函数
- **qmma.q** - Mathematica接口
- **tts.q** - 文本转语音

### 行情处理 (tick/)
- **u.q** - 行情订阅基础模块
- **r.q** - 行情读取模块
- **w.q** - 行情写入模块
- **chainedr.q** - 链式行情读取
- **chainedtick.q** - 链式行情处理

### 工具脚本 (util/)
- **fixdict.q** - FIX字典工具

## 核心数据结构

### 主要表结构
- **QX** - Level1静态数据表（合约信息、价格、成交量等）
- **O** - 订单表（包含订单的完整生命周期信息）
- **M** - 成交表（撮合成交记录）
- **P** - 持仓表（多空持仓信息）
- **QT** - 报价表（双边报价信息）
- **S** - 状态事件表
- **LOG** - 日志表
- **TASK** - 任务调度表

### 命名空间约定
- **.ex** - 交易所信息
- **.db** - 数据库相关
- **.ft** - 期货feed
- **.fq** - 行情feed
- **.fe** - 执行feed
- **.fc** - 期货feed
- **.fu** - 用户feed
- **.fa** - 算法feed
- **.fp** - 转发feed

## 启动脚本
- **startfcqtx** - 启动期货交易系统（Linux）
- **startfcqtx32** - 启动32位版本
- **stopfc** - 停止期货系统
- **mklinks.sh** - 创建符号链接

## 系统特性

### 高性能特性
- 基于kdb+内存数据库，提供微秒级延迟
- 支持多核并行处理（taskset绑定CPU核心）
- 内存映射文件系统支持

### 多市场支持
- 支持中国期货市场（CTP、XTP等接口）
- 支持股票市场（多种券商接口）
- 支持期权交易
- 支持外汇交易（UFX）

### 风险管理
- 实时风控模块（rcbase.q）
- 持仓监控和限制
- 订单风控检查

### 策略支持
- 内置多种算法策略
- 支持自定义策略开发
- 回测功能支持

## 部署说明
系统设计为在Linux环境下运行，使用kdb+作为核心数据库引擎。通过配置文件可以灵活配置不同的交易接口和策略参数。

## 版本信息
- 最新模块版本：2025.02.19
- 支持的kdb+版本：需要64位Linux版本
- 主要更新记录见 changelog.txt

该平台为专业量化交易提供了完整的基础设施，适合机构投资者和专业交易团队使用。
