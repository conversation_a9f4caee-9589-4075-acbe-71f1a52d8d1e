\d .enum 
loglevels:`error`warn`info`debug;
nulldict:(enlist `)!enlist (::);

`NULL`NEW`PARTIALLY_FILLED`FILLED`DONE_FOR_DAY`CANCELED`REPLACED`PENDING_CANCEL`STOPPED`REJECTED`SUSPENDED`PENDING_NEW`CALCULATED`EXPIRED`ACCEPTED_FOR_BIDDING`PENDING_REPLACE`REJECTED_CANCEL`REJECTED_REPLACE set' " 0123456789ABCDEYZ"; /OrdStatusMap
`AUTOMATED_EXECUTION_ORDER_PRIVATE_NO_BROKER_INTERVENTION`AUTOMATED_EXECUTION_ORDER_PUBLIC_BROKER_INTERVENTION_OK`MANUAL_ORDER_BEST_EXECUTION set' "123"; /HandlInstMap
`BUY`SELL`BUY_MINUS`SELL_PLUS`SELL_SHORT`SELL_SHORT_EXEMPT`D`CROSS`CROSS_SHORT`ETFCreation`ETFRedemption set'  "123456789CR"; /SideMap
`BROKER_ERROR`BROKER_OPTION`UNKNOWN_SYMBOL`EXCHANGE_CLOSED`ORDER_EXCEEDS_LIMIT`TOO_LATE_TO_ENTER`UNKNOWN_ORDER`DUPLICATE_ORDER`DUPLICATE_VERBALYES`STALE_ORDER  set' 999 0 1 2 3 4 5 6 7 8i; /OrdRejReasonMap
`BROKER_ERROR_CXL`TOO_LATE_TO_CANCEL`UNKNOWN_ORDER_CXL`BROKER_OPTION_CXL`ALREADY_PENDING set' 998 0 1 2 3i; /CxlRejReasonMap[`UNKNOWN_ORDER_CXL]
`UNKNOWN_SYMBOL_DK`WRONG_SIDE`QUANTITY_EXCEEDS_ORDER`NO_MATCHING_ORDER`PRICE_EXCEEDS_LIMIT`OTHER_DK set' "ABCDEZ"; /DKReasonMap[`UNKNOWN_SYMBOL_DK`OTHER_DK] 
`DAY`GOOD_TILL_CANCEL`AT_THE_OPENING`IMMEDIATE_OR_CANCEL`FILL_OR_KILL`GOOD_TILL_CROSSING`GOOD_TILL_DATE set' "0123456"; /TimeInForceMap
`MARKET`LIMIT`STOP`STOP_LIMIT`MARKET_ON_CLOSE`WITH_OR_WITHOUT`LIMIT_OR_BETTER`LIMIT_WITH_OR_WITHOUT`ON_BASIS`ON_CLOSE`LIMIT_ON_CLOSE`FOREX_MARKET`PREVIOUSLY_QUOTED`PREVIOUSLY_INDICATED`FOREX_LIMIT`FOREX_SWAP`FOREX_PREVIOUSLY_QUOTED`FUNARI`PEGGED  set' "123456789ABCDEFGHIP"; /OrdTypeMap
`CLOSE`FIFO`OPEN`ROLLED`CLOSE_BUT_NOTIFY_ON_OPEN`DEFAULT`CLOSETODAY`CLOSEYESTODAY set' "CFORNDTY"; /PositionEffect
`RUNNING`PAUSE`PAUSEWITHCANCEL`NOCHANGE set' "1234"; /StrategyStatus
`CANCEL`CORRECT`STATUS`NOE set' "123N"; /ExecTransTypeMap except `NEW
`PARTIAL_FILL`FILL`REPLACE`RESTATED set' "125D"; /ExecTypeMap except OrdStatusMap
`NONE`SEVERE`MEDIUM`INFO set' "0123"; /NotificationType
`USERREQUESTED`MARKETMOVEMENT`NONALGOSUITABLE`OTHERCOMPLIANCE`ONRECOVERY`ENDTIMEREACHED`USERATTENTIONREQ`ONALERT set' "12345678" ; /NotificationReason
`ACK`RESPONSE`TIMED`EXECSTARTED`ALLDONE`ALERT set' 1 2 3 4 5 6i; /ListStatusType
`INBIDDINGPROCESS`RECEIVEDFOREXECUTION`EXECUTING`CANCELLING`ALERT_LS`ALLDONE_LS`REJECT set' 1 2 3 4 5 6 7i; /ListOrderStatus[`ALERT_LS`ALLDONE_LS]
`CASH`MARGIN_OPEN`MARGIN_CLOSE`MARGIN_LOCK`MARGIN_REPAY set' "12345"; /CashMargin
\d .

//2023.12.18:新增`MARGIN_LOCK`MARGIN_REPAY枚举值