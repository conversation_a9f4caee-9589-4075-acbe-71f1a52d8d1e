.module.tsgrid:2017.04.26;
txload "tsl/tslib"; // 定义了一个名为tsgrid的模块，版本日期为2017年4月26日，并加载了tsl/tslib库

// 事件处理函数
// 行情事件处理函数
onq_grid:{[x;y];}; 

// 当一个订单完成后，它清理订单列表，更新成交计数，并触发策略重新计算(如果有必要)
// 成交回报事件处理函数
ono_grid:{[x;y] 
    opeg_libpeg[x;y]; // 可能是一个记录订单操作的库函数调用 
    r:.db.O[y]; // 从订单表.db.0中获取订单ID为y的记录
    st:r`status; // 提取订单状态 status
    s:r`sym; // 提取交易品种 sym
    // if[condition;action]   :()返回语句(什么都不做)    :返回操作符    ()空列表  
    // exec q-sql操作，从表中提取sym列
    // 如果订单未结束 或 品种不在策略的交易品种列表中，则直接返回
    if[not (r`end)&s in exec sym from .db.Ts[x;`TP];:()];
        
    // 条件操作符$[condition;trueresult;falseresult]
    // 判断订单方向，如果是卖出单，其状态键为'oidup',买入单为'oiddn'
    sd:$[r[`side]=.enum`SELL;`oidup;`oiddn];

    // 从对应品种中的'oidup'或'oiddn'列中删除订单ID为y的记录
    .db.Ts[x;`TP;s;sd]:.db.Ts[x;`TP;s;sd] except y;

    // 如果订单状态是"已成交"(FILLED)，则对应的更新方向(nup/ndn)的成交订单数
    if[st=.enum.FILLED;.db.Ts[x;`TP;s;$[sd=`oidup;`nup;`ndn]]+:1];

    // 如果该品种的策略未停止，则调用'grid_check'检查是否需要更新的操作
    if[not .db.Ts[x;`TP;s;`stop];grid_check[x;s]];
    }; /[tid;oid]

// 找到目标策略，清除活跃订单记录并重置报价信息 
// 日滚事件处理函数
onr_grid:{[x;y]
    .db.Ts[x;`TP]:update 
        oidup:{`symbol$()} each oidup,  // 清空卖出挂单的ID列表 对oidup列中的每一个元素执行匿名函数`symbol$()
        oiddn:{`symbol$()} each oiddn,  // 清空买入挂单的ID列表 对oidin列中的每一个元素执行匿名函数`symbol$()
        // 将报价和数量重置为"空"或"无效"状态
        pxask:0n,  
        qtyask:0n,
        pxbid:0n,
        qtybid:0n 
    from .db.Ts[x;`TP];
    };

// 定时器事件处理函数
ont_grid:{[x;y]
    t:`time$y; /提取交易时间
    grid_check[x] each exec sym from .db.Ts[x;`TP] where istrading[t] each sym,not stop; // 对于当前交易品种中所有正在交易且未停止的x,使用grid_check进行检查
    }; /[tid;.z.P] oexpire_libpeg[x;y];

// 如果订单状态是待取消，取消订单
grid_cxl:{[x]
    if[not .db.O[x;`cstatus]=.enum`PENDING_CANCEL; cxlord x];
    };

// 网格策略函数
grid_check:{[x;s]
    // 初始化和数据获取
    r:.db.Ts[x;`TP;s]; // 获取标的s的交易设置
    h:.db.QX[s]; // 获取标的s的行情数据
    if[any null h`bid`ask;:()]; // 如果买卖价缺失，则直接返回
    p:(0.5*sum h`bid`ask)^h`price; // 计算中间价或使用最新价
    sp:h[`ask]-h[`bid]; // 计算价差
    q0:netpos[x;s]; // 获取标的s的持仓数量
    d:signum[q0]; // 获取持仓方向
    oul:r`oidup; // 获取卖出挂单的ID列表
    odl:r`oiddn; // 获取买入挂单的ID列表
    ct:0b; // 表示取消操作的状态
    
    // 平仓条件检查
    // 如果持有多头并且价格到达平仓区域，则平多仓；如果持有空头且价格到达平仓区域，则平空仓；只有在市场流动性充足的情况下才执行平仓
    if[(((d>0)&p within r`closelongrange)|((d<0)&p within r`closeshortrange))&(sp<=r`spreadmax);
        // 如果卖出订单列表为空，取消所有卖出订单
        if[count oul;grid_cxl each oul;ct:1b];
        // 如果买入订单列表为空，取消所有买入订单
        if[count odl;grid_cxl each odl;ct:1b];
        if[ct;:()];
        pegord_libpeg each ($[d>0;limit_sell;limit_buy])[x;s;abs[q0];$[d>0;h`bid;h`ask];`stop]; // 执行平仓, pegord_libpeg可能是订单处理库函数
        .db.Ts[x;`TP;s;`stop]:1b;
        :()];
    
    // 网格参数计算
    sup:r`stepup; sdn:r`stepdn; // 网格上下步长
    nu:r`nup; nd:r`ndn; // 网格上下层数
    .db.Ts[x;`TP;s;`pxref]:pr:r[`pxbase]+(sup*nu)-(sdn*nd); // 计算参考价格 pxbase网格参考价格
    qus:r`qtyups; // 已成交的卖出数量
    qdl:r`qtydnl; // 已成交的买入数量
    .db.Ts[x;`TP;s;`qtyref]:qr:r[`qtybase]+(qdl*nd)-(qus*nu); // 计算参考数量
    .db.Ts[x;`TP;s;`pxbid]:pb:pr-sdn; // 计算买入价格
    .db.Ts[x;`TP;s;`pxask]:pa:pr+sup; // 计算卖出价格
    xb:$[0=count odl;0n;.db.O[odl[0];`price]]; // 获取实际可以买入的价格
    xa:$[0=count oul;0n;.db.O[oul[0];`price]]; // 获取实际可以卖出的价格 
    
    // 挂单价格验证
    if[(not null xb)&(pb<>xb);grid_cxl each odl;ct:1b]; // 实际买入非空并且计算买价不等于实际买价
    if[(not null xa)&(pa<>xa);grid_cxl each oul;ct:1b]; // 实际卖出非空并且计算卖价不等于实际卖价
    if[ct;:()];
    
    // 新挂单逻辑
    .db.Ts[x;`TP;s;`qtybid]:qb:(r[`possup]&qr+qdl)-q0; // possup 允许最大持仓 设置买盘挂单量
    if[(0=count odl)&(0<qb)&(pb>=h`inf);  
    .db.Ts[x;`TP;s;`oiddn]:raze limit_buy[x;s;qb;pb;`dn]]; // 如果没有下行挂单（odl）且需要挂买单（qb>0）且价格合理（pb>=inf），则挂限价买单
    .db.Ts[x;`TP;s;`qtyask]:qa:q0-(r[`posinf]|qr-qus); // posinf 允许最大持仓 设置卖盘挂单量
    if[(0=count oul)&(0<qa)&(pa<=h`sup);
    .db.Ts[x;`TP;s;`oidup]:raze limit_sell[x;s;qa;pa;`up]]; // 如果没有上行挂单（oul）且需要挂卖单（qa>0）且价格合理（pa<=sup），则挂限价卖单
    };

.db.Ts.qtx:.enum.nulldict;
.db.Ts.qtx[`active`acc`accx`stop`event]:(0b;`ctp;`symbol$();0b;.enum.nulldict);
.db.Ts.qtx.event[`timer`quote`exerpt`dayroll`sysinit`sysexit]:`ont_grid`onq_grid`ono_grid`onr_grid``;
.db.Ts.qtx[`Cp]:`tmout`tmout1`tmout2`urge!(00:00:05;00:00:10;00:00:15;2);
.db.Ts.qtx[`syms`TP]:(`symbol$();
    ([sym:`symbol$()];
     stop:`boolean$();
     logpx:`boolean$();
     pxbase:`float$();
     qtybase:`float$();
     nup:`long$();
     ndn:`long$();
     nos:`long$();
     pxref:`float$();
     qtyref:`float$();
     pxask:`float$();
     qtyask:`float$();
     pxbid:`float$();
     qtybid:`float$();
     stepup:`float$();
     stepdn:`float$();
     qtyups:`float$();
     qtydnl:`float$();
     posinf:`float$();
     possup:`float$();
     stepupmap:();
     stepdnmap:();
     qtyupsmap:();
     qtydnlmap:();
     spreadmax:`float$();
     closelongrange:();
     closeshortrange:();
     oidup:();
     oiddn:())
     ); 

// 添加了4个价差合约配置：合约命名规则 SP 合约1&合约2，交易所
// 例如：SP i1907&i1909.XDCE 表示大连商品交易所的i1907和i1909合约价差

.db.Ts.qtx.TP,:((`$"SP i1907&i1909.XDCE";0b;0b;60f;2f;1f;0f;0f;()!();1.5;-2 -1f;85 90f;();());
    (`$"SP i1909&i1911.XDCE";0b;0b;-10f;2f;1f;0f;0f;()!();1.5;-2 -1f;110 120f;();());
    (`$"SP i1911&i2001.XDCE";0b;0b;12f;2f;1f;0f;0f;()!();1.5;-2 -1f;110 120f;();());
    (`$"SP i1909&i2001.XDCE";0b;0b;40f;2f;1f;0f;0f;()!();1.5;-2 -1f;110 120f;();())
    );

// 添加了2个单个合约的配置
// c2001.XDCE: 大连商品交易所的c2001合约
.db.Ts.qtx.TP,:(`c2001.XDCE;0b;0b;1843f;0f;0;0;0n;0n;0n;0n;0n;0n;3f;6f;1f;2f;-10f;10f;()!();()!();()!();()!();2f;1570 1600f;2000 2030f;();());
.db.Ts.qtx.TP,:(`TA001.XZCE;0b;0b;4758f;0f;0;0;0n;0n;0n;0n;0n;0n;10f;20f;1f;2f;-10f;10f;()!();()!();()!();()!();5f;4570 4600f;5000 5030f;();());

\
noeexec[`20170201001;`ft`qtx;`XAUUSD.METAL;.enum`SELL;.enum`OPEN;66f;1232.436;"init"];
noeexec[`20170313002;`ft`qtx;`ZC701.XZCE;.enum`SELL;.enum`OPEN;1f;582.2;"init"];

ont_grid:{[x;y]t:`time$y;wd:weekday[y];r:.db.Ts[x];acc:r`acc;z:r`xsym;pa:.db.QX[z;`ask];pb:.db.QX[z;`bid];if[(not any t within/: r`TRDTIME)|(wd=6)|((wd=5)&t>06:00)|((wd=0)&t<06:00)|(0>=pa&pb);:()];pm:-0w^first r`LONGSTOPRANGE;pM:0w^first r`SHORTSTOPRANGE;sm:r`SPREADMAX;$[((pb>pM)|(pa<pm))&(sm>=pa-pb);$[0<count il:exec id from .db.O where ts=x,not end,(ref in `up`dn)|00:00:05<.z.P-ntime;cxlord each il;[if[(pa within r`SHORTSTOPRANGE)&(sm>=pa-pb)&(0<q:neg .db.P[(x;acc;z);`sqty]);limit_buy[x;z;q;pa+sm;`stopcloseshort];.db.Ts[x;`mode]:`MANUAL];if[(pb within r`LONGSTOPRANGE)&(sm>=pa-pb)&(0<q:.db.P[(x;acc;z);`lqty]);limit_sell[x;z;q;pb-sm;`stopcloselong];.db.Ts[x;`mode]:`MANUAL];]];[pq:sum 0f^.db.P[(x;acc;z);`lqty`sqty];qu:r`GRIDSIZE;ps:1f^ffill r`PRICESCALE;pu:r[`ANCHORUP]+r[`SHORTOFFSET];if[(null r`OIDUP)&(pq>r`POSINF)&(pu<=0w^.db.QX[z;`sup]);.db.Ts[x;`OIDUP]:first limit_sell[x;z;qu;ps*pu;`up]];pd:r[`ANCHORDN]+r[`LONGOFFSET];if[(null r`OIDDN)&(pq<r`POSSUP)&(pd>=0f^.db.QX[z;`inf]);.db.Ts[x;`OIDDN]:first limit_buy[x;z;qu;ps*pd;`dn]]]];}; /[tid;.z.P]

grid_check:{[x;s]r:.db.Ts[x;`TP;s];h:.db.QX[s];if[any null h`bid`ask;:()];p:(0.5*sum h`bid`ask)^h`price;sp:h[`ask]-h[`bid];q0:netpos[x;s];d:signum[q0];oul:r`oidup;odl:r`oiddn;if[(((d>0)&p within r`closelongrange)|((d<0)&p within r`closeshortrange))&(sp<=r`spreadmax);ct:0b;if[count oul;grid_cxl each oul;ct:1b];if[count odl;grid_cxl each odl;ct:1b];if[ct;:()];pegord_libpeg each ($[d>0;limit_sell;limit_buy])[x;s;abs[q0];$[d>0;h`bid;h`ask];`stop];.db.Ts[x;`TP;s;`stop]:1b;:()];sz:r`size;ps:r`pxstep;q:d*sz*n:floor abs[q0]%sz;qr:q-q0;p0:r[`pxbase]-d*n*ps;pb:h[`bid]&p0-ps;pa:h[`ask]|p0+ps;xb:$[0=count odl;0n;.db.O[odl[0];`price]];xa:$[0=count oul;0n;.db.O[oul[0];`price]];if[(not null xb)&(pb<>xb);grid_cxl each odl];if[(not null xa)&(pa<>xa);grid_cxl each oul];q:(sz+0&qr)&r[`possup]-q0;if[(0=count odl)&(0<q)&(pb>=h`inf);.db.Ts[x;`TP;s;`oiddn]:raze limit_buy[x;s;q;pb;`dn]];q:(sz+0&qr)&q0-r`posinf;if[(0=count oul)&(0<q)&(pa>=h`sup);.db.Ts[x;`TP;s;`oidup]:raze limit_sell[x;s;q;pa;`up]];}; /[tid;sym]

\
数据流分析
1. 数据输入源
行情数据 (.db.QX): 实时买卖报价和最新成交价

订单数据 (.db.O): 订单状态和执行情况

持仓数据 (netpos): 当前净持仓数量

配置数据 (.db.Ts.qtx.TP): 策略参数配置

2. 事件驱动流程
📈 行情事件 → onq_grid
触发条件: 新行情数据到达
处理逻辑: 可在此处添加价格突变检测、波动率计算等

✅ 成交回报事件 → ono_grid
触发条件: 订单成交或状态变化
处理逻辑:
记录订单操作
从挂单列表中移除已完成订单
更新成交次数计数器(nup/ndn)
触发grid_check重新计算网格

🔄 日滚事件 → onr_grid
触发条件: 交易日切换
处理逻辑: 重置所有状态，准备新交易日的交易

网格策略