﻿/////////////////////////////////////////////////////////////////////////
///@system 今古交易系统
///@company 深圳市今古科技有限公司
///@file JGtdcUserApiStruct.h
///@brief 接口使用的业务数据类型定义
///@history 
///2018-02-24	zl	
/////////////////////////////////////////////////////////////////////////


#if !defined(JG_TDCDATATYPE_H)
#define JG_TDCDATATYPE_H

#if _MSC_VER > 1000
#pragma once 
#endif  // _MSC_VER
#define __int64 long long

typedef char TJGtdcLoginType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcLoginType 登陆类型
//////////////////////////////////////////////////////////////////////////
///资金账号
#define	JG_TDC_LOGINTYPE_FundAccount	'0'
///客户号
#define	JG_TDC_LOGINTYPE_ClientID		'1'
///股东代码
#define	JG_TDC_LOGINTYPE_StockAccount	'2'
///只做查询
#define	JG_TDC_LOGINTYPE_OnlyQuery		'3'

typedef int TJGtdcExchangeType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchangeType 市场类型
//////////////////////////////////////////////////////////////////////////
///深A
#define	JG_TDC_EXCHANGETYPE_SZA		1	
///沪A
#define	JG_TDC_EXCHANGETYPE_SHA		2
///深B
#define	JG_TDC_EXCHANGETYPE_SZB		3
///沪B
#define	JG_TDC_EXCHANGETYPE_SHB		4
///特转A
#define	JG_TDC_EXCHANGETYPE_TZA		5
///特转B
#define	JG_TDC_EXCHANGETYPE_TZB		6
///个股期权深A
#define	JG_TDC_EXCHANGETYPE_OPTSZA	11
///个股期权沪A
#define	JG_TDC_EXCHANGETYPE_OPTSHA	12
/// 沪港通
#define JG_TDC_EXCHANGETYPE_SHHK  13
/// 深港通
#define JG_TDC_EXCHANGETYPE_SZHK  14 
///金融期货
#define	JG_TDC_EXCHANGETYPE_CFFEX	100	
///上海商品期货
#define	JG_TDC_EXCHANGETYPE_SHFE	101
///郑州商品期货
#define	JG_TDC_EXCHANGETYPE_CZCE	102
///大连商品期货
#define	JG_TDC_EXCHANGETYPE_DCE		103

typedef char TJGtdcMoneyType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMoneyType 币种
//////////////////////////////////////////////////////////////////////////
///人民币
#define	JG_TDC_MONEYTYPE_RMB	'0'
///港币
#define	JG_TDC_MONEYTYPE_HKD	'1'
///美元
#define	JG_TDC_MONEYTYPE_USD	'2'

typedef char TJGtdcHolderStatus;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcHolderStatus 股东状态
//////////////////////////////////////////////////////////////////////////
///正常
#define	JG_TDC_HOLDERSTATUS_Use		'0'
///冻结
#define	JG_TDC_HOLDERSTATUS_Frozen	'1'

typedef char TJGtdcMainFlag;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMainFlag 股东主副标志
//////////////////////////////////////////////////////////////////////////
///副账号
#define	JG_TDC_MAINFLAG_Second	'0'
///主账号
#define	JG_TDC_MAINFLAG_First	'1'

typedef char TJGtdcAccountRights;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAccountRights 账号权限(按位)
//////////////////////////////////////////////////////////////////////////
///信用账号
#define	JG_TDC_ACCOUNTRIGHTS_Credit	0x01
///普通账号
#define	JG_TDC_ACCOUNTRIGHTS_Normal	0x02
///期货账号
#define	JG_TDC_ACCOUNTRIGHTS_Future	0x04
///期权账号
#define	JG_TDC_ACCOUNTRIGHTS_Option	0x08

typedef int TJGtdcSubType;
/////////////////////////////////////////////////////////////////////////
///< TJGtdcSubType cSubType            订阅类型 订阅 or 取消订阅
/////////////////////////////////////////////////////////////////////////
///订阅数据
#define	JG_TDC_SUBTYPE_Subscription			'0'
///取消订阅数据
#define	JG_TDC_SUBTYPE_UnSubscription		'1'


typedef int TJGtdcSupportSubType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcSupportSubType  SubDataType	  订阅数据类型(按位与)
/////////////////////////////////////////////////////////////////////////
///成交主推
#define	JG_TDC_SUB_DATA_Business	0x01
///委托主推
#define	JG_TDC_SUB_DATA_Entrust	    0x02
///可撤单主推
#define	JG_TDC_SUB_DATA_ReCancel	0x04
///持仓主推
#define	JG_TDC_SUB_DATA_Hold		0x08
///资金主推
#define	JG_TDC_SUB_DATA_Fund		0x10

typedef int TJGtdcTradeType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTradeType 交易类型
//////////////////////////////////////////////////////////////////////////
///普通买入
#define	JG_TDC_TRADETYPE_Buy				   1	
///普通卖出
#define	JG_TDC_TRADETYPE_Sell				   2
///ETF申购
#define	JG_TDC_TRADETYPE_ETFApply			   3
///ETF赎回
#define	JG_TDC_TRADETYPE_ETFRedeem			   4
///跨市ETF申购
#define	JG_TDC_TRADETYPE_ETFTranApply		   5
///跨市ETF赎回
#define	JG_TDC_TRADETYPE_ETFTranRedeem		   6
///分级基金合并
#define	JG_TDC_TRADETYPE_StructuredFundMarge   7
///分级基金拆分
#define	JG_TDC_TRADETYPE_StructuredFundSplit   8
///LOF申购
#define	JG_TDC_TRADETYPE_LOFApply			   9
///LOF赎回
#define	JG_TDC_TRADETYPE_LOFRedeem			   10
///融资买入
#define	JG_TDC_TRADETYPE_LoanBuy			  11	
///融券卖出
#define	JG_TDC_TRADETYPE_LoanSell			  12
///买券还券
#define	JG_TDC_TRADETYPE_EnBuyBack			  13	
///卖券还款
#define	JG_TDC_TRADETYPE_EnSellBack			  14
///直接还券
#define	JG_TDC_TRADETYPE_StockBack			  15
///直接还款
#define	JG_TDC_TRADETYPE_PayBack			  16
///担保品划入
#define	JG_TDC_TRADETYPE_MortgageIn			  21
///担保品划出
#define	JG_TDC_TRADETYPE_MortgageOut		  22
///认购行权
#define	JG_TDC_TRADETYPE_CallExercise		  31
///认沽行权
#define	JG_TDC_TRADETYPE_PutExercise		  32
///证券锁定
#define	JG_TDC_TRADETYPE_StockLock			  33
///证券解锁
#define	JG_TDC_TRADETYPE_StockUnLock		  34
///认购自动行权
#define	JG_TDC_TRADETYPE_AutoCallExercise	  35
///认沽自动行权
#define	JG_TDC_TRADETYPE_AutoPutExercise	  36
///跨境ETF申购
#define	JG_TDC_TRADETYPE_ETFCrossApply		  50
///跨境ETF赎回
#define	JG_TDC_TRADETYPE_ETFCrossRedeem		  51
///货币基金申购
#define	JG_TDC_TRADETYPE_MoneyFundApply		  52
///货币基金赎回
#define	JG_TDC_TRADETYPE_MoneyFundRedeem	  53
///新股申购
#define JG_TDC_TRADETYPE_NewStockApply		  60
///配售申购
#define JG_TDC_TRADETYPE_ValueAllotApply	  61

typedef int TJGtdcPriceType; 
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPriceType 价格类型                                            
//////////////////////////////////////////////////////////////////////////
///限价
#define	JG_TDC_PRICETYPE_Limit			1	
///对方最优价格
#define	JG_TDC_PRICETYPE_OtherBest		2
///本方最优价格
#define	JG_TDC_PRICETYPE_Best			3
///即时成交剩余撤销
#define	JG_TDC_PRICETYPE_Timely			4
///最优五档剩余撤销
#define	JG_TDC_PRICETYPE_BestOrCancel	5
///全额成交或撤销
#define	JG_TDC_PRICETYPE_FillOrCancel	6
///最优五档剩余转限价
#define	JG_TDC_PRICETYPE_BestOrLimit	7
///市价剩余转限价
#define	JG_TDC_PRICETYPE_MarketOrLimit	8
///全额即时限价
#define	JG_TDC_PRICETYPE_FillOrLimit	9
/// 竞价限价
#define JG_TDC_PRICETYPE_CPLimit  10 
/// 增强限价
#define JG_TDC_PRICETYPE_EPLimit 11
/// 零股限价
#define JG_TDC_PRICETYPE_ZSLimit 12 
/// 盘后定价
#define JG_TDC_PRICETYPE_AfterMarketFix 13

typedef int TJGtdcResultType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcResultType 应答结果
//////////////////////////////////////////////////////////////////////////
///成功 
#define	JG_TDC_ANSRESULT_Success			0
///柜台返回错误 
#define	JG_TDC_ANSRESULT_Error				-1


typedef int TJGtdcQueryMode;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcQueryMode 查询模式
//////////////////////////////////////////////////////////////////////////
///查询全部
#define	JG_TDC_QUERYMODE_All					0
///按资金账号查询
#define	JG_TDC_QUERYMODE_ByFundAccount			1
///按市场查询
#define	JG_TDC_QUERYMODE_ByExchange				2
///按证券代码或合约代码查询
#define	JG_TDC_QUERYMODE_ByCode					3
///按委托合同号查询
#define	JG_TDC_QUERYMODE_ByEntrustNo			4
///按币种查询
#define	JG_TDC_QUERYMODE_ByMoneyType			5
///按负债现状查询
#define	JG_TDC_QUERYMODE_ByDebitStatus			6
///按合约编码查询
#define	JG_TDC_QUERYMODE_ByOptionNumber			7
///按期权类型查询
#define	JG_TDC_QUERYMODE_ByOptionType			8
///按期权持仓类别查询
#define	JG_TDC_QUERYMODE_ByOptionHoldType		9
///按组合ID查询
#define	JG_TDC_QUERYMODE_ByComboID				10
///按银行代码查询
#define	JG_TDC_QUERYMODE_ByBankCode				11
///按服务器类型查询
#define	JG_TDC_QUERYMODE_ByServerType			12
///按服务器子类型查询
#define	JG_TDC_QUERYMODE_BySubServerType		13
///按行权策略类型查询
#define	JG_TDC_QUERYMODE_ByExerciseStrategyType	14
///按终端类型查询
#define	JG_TDC_QUERYMODE_ByTerminalType			15
///按终端子类型查询
#define	JG_TDC_QUERYMODE_BySubTerminalType		16
///<按索引查询
#define JG_TDC_QUERYMODE_ByIndexEntrustNo       17  

typedef int TJGtdcQueryMark;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcQueryMark 查询标记
//////////////////////////////////////////////////////////////////////////
///信用账户查询普通账户
#define	JG_TDC_QUERYMARK_Normal	 0
///普通账户查询信用账户
#define	JG_TDC_QUERYMARK_Credit	 1

typedef int TJGtdcQueryDirection;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcQueryDirection 查询方向
//////////////////////////////////////////////////////////////////////////
///倒序（往后翻，查询更早的信息）
#define	JG_TDC_QUERYDIRECTION_Inverted	'0'
///顺序（往前翻，查询更晚的信息）
#define	JG_TDC_QUERYDIRECTION_Sequence	'1'


typedef char TJGtdcEntrustStatus;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEntrustStatus 委托状态
//////////////////////////////////////////////////////////////////////////
///未报
#define	JG_TDC_ENTRUSTSTATUS_NotReport			'0'
///正报
#define	JG_TDC_ENTRUSTSTATUS_Reporting			'1'
///已报
#define	JG_TDC_ENTRUSTSTATUS_Reported			'2'
///已报待撤
#define	JG_TDC_ENTRUSTSTATUS_Canceling			'3'
///部成待撤
#define	JG_TDC_ENTRUSTSTATUS_PartFilledCanceling	'4'
///部撤
#define	JG_TDC_ENTRUSTSTATUS_PartFilledCanceled	'5'
///已撤
#define	JG_TDC_ENTRUSTSTATUS_Canceled			'6'
///部成
#define	JG_TDC_ENTRUSTSTATUS_PartFilled			'7'
///已成
#define	JG_TDC_ENTRUSTSTATUS_AllFilled			'8'
///废单
#define	JG_TDC_ENTRUSTSTATUS_Invalid			'9'
///待报
#define	JG_TDC_ENTRUSTSTATUS_Queueing			'a'
///场内拒绝
#define	JG_TDC_ENTRUSTSTATUS_Rejected			'b'

typedef char TJGtdcBusinessStatus;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBusinessStatus 成交状态
//////////////////////////////////////////////////////////////////////////
///普通成交
#define	JG_TDC_BUSINESSSTATUS_Filled	'0'
///撤单成交
#define	JG_TDC_BUSINESSSTATUS_Canceled	'1'
///废单成交
#define	JG_TDC_BUSINESSSTATUS_Invalid	'2'

typedef char TJGtdcStockBackMode;
/////////////////////////////////////////////////////////////////////////
///TJGtdcStockBackMode	还券模式
/////////////////////////////////////////////////////////////////////////
///直接还券
#define	JG_TDC_STOCKBACKMODE_Direct		'0'
///意向还券
#define	JG_TDC_STOCKBACKMODE_Will		'1'
///即时还券
#define	JG_TDC_STOCKBACKMODE_Immediate	'2'


typedef int TJGtdcObjectRights;
/////////////////////////////////////////////////////////////////////////
///TJGtdcObjectRights	标的权限（按位判断权限）
/////////////////////////////////////////////////////////////////////////
///融资标的
#define	JG_TDC_OBJECTRIGHTS_Finance		0x01
///融券标的
#define	JG_TDC_OBJECTRIGHTS_Shortsell	0x02
///担保品标的
#define	JG_TDC_OBJECTRIGHTS_Mortgage	0x04


typedef char TJGtdcDebitStatus;
/////////////////////////////////////////////////////////////////////////
///TJGtdcDebitStatus	负债现状
/////////////////////////////////////////////////////////////////////////
///未了结
#define	JG_TDC_DEBITSTATUS_NotFinish	'0'
///已了结
#define	JG_TDC_DEBITSTATUS_Finish		'1'
///到期未平仓
#define	JG_TDC_DEBITSTATUS_DueNotPayOff	'2'


typedef char TJGtdcMortgageFlag;
/////////////////////////////////////////////////////////////////////////
///TJGtdcMortgageFlag	担保品标志
/////////////////////////////////////////////////////////////////////////
///非担保品
#define	JG_TDC_MORTGAGEFLAG_No		'0'
///是担保品
#define	JG_TDC_MORTGAGEFLAG_Yes		'1'

typedef char TJGtdcOffsetType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcOffsetType	开平仓类型
/////////////////////////////////////////////////////////////////////////
///开仓
#define	JG_TDC_OFFSETTYPE_Open			'0'
///平仓
#define	JG_TDC_OFFSETTYPE_PayOff		'1'
///平今仓
#define	JG_TDC_OFFSETTYPE_PayOffToday	'2'

typedef char TJGtdcHedgeType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcHedgeType	套保类型
/////////////////////////////////////////////////////////////////////////
///投机
#define	JG_TDC_HEDGETYPE_Speculation	'0'
///套利
#define	JG_TDC_HEDGETYPE_Arbitrage		'1'
///保值
#define	JG_TDC_HEDGETYPE_Hedge			'2'


typedef char TJGtdcStockType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcStockType	证券类别
/////////////////////////////////////////////////////////////////////////
///股票
#define	JG_TDC_STOCKTYPE_Stock		'0'
///ETF基金
#define	JG_TDC_STOCKTYPE_ETF		'1'
///ETF基金
#define	JG_TDC_STOCKTYPE_Option		'2'


typedef char TJGtdcOptionType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcOptionType	期权类别
/////////////////////////////////////////////////////////////////////////
///认购
#define	JG_TDC_OPTIONTYPE_Call		'0'
///认沽
#define	JG_TDC_OPTIONTYPE_Put		'1'


typedef char TJGtdcOptionHoldType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcOptionHoldType	期权持仓类别
/////////////////////////////////////////////////////////////////////////
///权利仓
#define	JG_TDC_OPTIONHOLDTYPE_Right		'0'
///义务仓
#define	JG_TDC_OPTIONHOLDTYPE_Duty		'1'
///备兑仓
#define	JG_TDC_OPTIONHOLDTYPE_Covered	'2'


typedef char TJGtdcCoveredType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcCoveredType	备兑标识
/////////////////////////////////////////////////////////////////////////
///非备兑
#define	JG_TDC_COVEREDTYPE_No		'0'
///备兑
#define	JG_TDC_COVEREDTYPE_Yes		'1'


typedef char TJGtdcOptionStatus;
/////////////////////////////////////////////////////////////////////////
///TJGtdcOptionStatus	期权状态
/////////////////////////////////////////////////////////////////////////
///正常
#define	JG_TDC_OPTIONSTATUS_Normal		    '0'
///临时停牌
#define	JG_TDC_OPTIONSTATUS_Suspended	    '1'
///长期停牌
#define	JG_TDC_OPTIONSTATUS_LongSuspended	'2'


typedef char TJGtdcOptionMode;
/////////////////////////////////////////////////////////////////////////
///TJGtdcOptionMode	期权模式
/////////////////////////////////////////////////////////////////////////
///欧式
#define	JG_TDC_OPTIONMODE_European		'0'
///美式
#define	JG_TDC_OPTIONMODE_American		'1'


typedef char TJGtdcOpenType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcOpenType	开仓标识
/////////////////////////////////////////////////////////////////////////
///未开仓
#define	JG_TDC_OPENTYPE_No		'0'
///开仓
#define	JG_TDC_OPENTYPE_Yes		'1'


typedef char TJGtdcSuspendedType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcSuspendedType	停牌标识
/////////////////////////////////////////////////////////////////////////
///未停牌
#define	JG_TDC_SUSPENDEDTYPE_No		'0'
///停牌
#define	JG_TDC_SUSPENDEDTYPE_Yes	'1'


typedef char TJGtdcExpireType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcExpireType	到期日标识
/////////////////////////////////////////////////////////////////////////
///非到期日
#define	JG_TDC_EXPIRETYPE_No		'0'
///到期日
#define	JG_TDC_EXPIRETYPE_Yes		'1'


typedef char TJGtdcAdjustType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcAdjustType	调整标识
/////////////////////////////////////////////////////////////////////////
///未调整
#define	JG_TDC_ADJUSTTYPE_No		'0'
///调整
#define	JG_TDC_ADJUSTTYPE_Yes		'1'


typedef int TJGtdcBankType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcBankType	银行类型
/////////////////////////////////////////////////////////////////////////


typedef int TJGtdcBankRights;
/////////////////////////////////////////////////////////////////////////
///TJGtdcBankRights	银行权限（按位判断权限）
/////////////////////////////////////////////////////////////////////////
///银行密码
#define	JG_TDC_BANKRIGHTS_BankPassword		0x01
///资金密码
#define	JG_TDC_BANKRIGHTS_FundPassword		0x02
///交易密码
#define	JG_TDC_BANKRIGHTS_TradePassword		0x04


typedef char TJGtdcOperType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcOperType	操作方向
/////////////////////////////////////////////////////////////////////////
///转入（银行转券商）
#define	JG_TDC_OPERTYPE_BankToBranch		'1'	
///转出（券商转银行）
#define	JG_TDC_OPERTYPE_BranchToBank		'2'
///查询银行余额
#define	JG_TDC_OPERTYPE_QryBankBalance		'3'



typedef int TJGtdcExerciseStrategyType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcExerciseStrategyType	行权策略类型
/////////////////////////////////////////////////////////////////////////
///实值X元即行权
#define	JG_TDC_EXERCISESTRATEGYTYPE_InMoneyX		1	
///盈利X元即行权
#define	JG_TDC_EXERCISESTRATEGYTYPE_IncomeX			2
///盈利百分比即行权
#define	JG_TDC_EXERCISESTRATEGYTYPE_IncomePercent	3
///亏损百分比即行权
#define	JG_TDC_EXERCISESTRATEGYTYPE_LossPercent		4
///<实值X%即行权
#define JG_TDC_EXERCISESTRATEGYTYPE_InMoneyXPercent 5


typedef int TJGtdcDealStatus;
/////////////////////////////////////////////////////////////////////////
///TJGtdcDealStatus	处理状态
/////////////////////////////////////////////////////////////////////////
///未报
#define	JG_TDC_DEALSTATUS_NotReport		1	
///正在处理
#define	JG_TDC_DEALSTATUS_Dealing		2
///处理成功
#define	JG_TDC_DEALSTATUS_DealSuccess	3
///处理失败
#define	JG_TDC_DEALSTATUS_DealFail		4
///正在冲正
#define	JG_TDC_DEALSTATUS_Rushing		5
///冲正成功
#define	JG_TDC_DEALSTATUS_RushSuccess	6
///冲正失败
#define	JG_TDC_DEALSTATUS_RushFail		7



typedef int TJGtdcSettlementMode;
/////////////////////////////////////////////////////////////////////////
///TJGtdcSettlementMode		///< 帐单模式
/////////////////////////////////////////////////////////////////////////
///日结
#define	JG_TDC_SETTLEMENTMODE_Date		        1
///月结
#define	JG_TDC_SETTLEMENTMODE_Month	            2


typedef int TJGtdcSettlementType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcSettlementType		///< 帐单类型
/////////////////////////////////////////////////////////////////////////
///文件
#define	JG_TDC_SETTLEMENTTYPE_File		        1
///结构体
#define	JG_TDC_SETTLEMENTTYPE_Struct	        2
///JZOP-结构体类型
#define	JG_TDC_SETTLEMENTTYPE_JZOP	            3


typedef int TJGtdcDigestType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcDigestType			///< 业务类型
/////////////////////////////////////////////////////////////////////////
///证券买卖
#define JG_TDC_DIGEST_STOCKBS				0
///证券配股
#define JG_TDC_DIGEST_STOCKALLOTMENT		1
///配股入账
#define JG_TDC_DIGEST_STOCKALLOTMENTIN		2
///红股入账
#define JG_TDC_DIGEST_STOCKBONUSIN			3
///新股入账
#define JG_TDC_DIGEST_STOCKNEWIN			4
///个股期权
#define JG_TDC_DIGEST_OPTION				5


typedef char TJGtdcTransDirection;
/////////////////////////////////////////////////////////////////////////
///TJGtdcTransDirection  	///< 调拨方向
/////////////////////////////////////////////////////////////////////////
///UF2.0拨入UFT
#define JG_TDC_DIGEST_UF2TOUFT				0
///UFT拨出到UF2.0
#define JG_TDC_DIGEST_UFTTOUF2				1


typedef char TJGtdcCombSplitTag;
/////////////////////////////////////////////////////////////////////////
///TJGtdcCombSplitTag		///< 拆分标识
/////////////////////////////////////////////////////////////////////////
///合
#define JG_TDC_COMB_MERGER			'H'
///分
#define JG_TDC_COMB_SPLIT			'F'


typedef char TJGtdcInterceptionTag;
/////////////////////////////////////////////////////////////////////////
///TJGtdcInterceptionTag	///< 强拆标志
/////////////////////////////////////////////////////////////////////////
///不强拆
#define JG_TDC_COMB_UNINTERCE		'0'
///强拆
#define JG_TDC_COMB_INTERCE			'1'


typedef char TJGtdcBSTag;
/////////////////////////////////////////////////////////////////////////
///TJGtdcBSTag				 ///< 买卖标记
/////////////////////////////////////////////////////////////////////////
///买
#define JG_TDC_BSTAG_BUY			0
///卖
#define JG_TDC_BSTAG_SELL			1


typedef char TJGtdcPaymentFlag;
/////////////////////////////////////////////////////////////////////////
///TJGtdcPaymentFlag		///< 收付标志
/////////////////////////////////////////////////////////////////////////
///收
#define JG_TDC_COMB_INFUND			'I'
///付
#define JG_TDC_COMB_OUTFUND			'O'


typedef char TJGtdcConvertOption;
/////////////////////////////////////////////////////////////////////////
///TJGtdcConvertOption;							///< 转账选项
/////////////////////////////////////////////////////////////////////////
///不用输入密码
#define JG_TDC_CONVERT_NEED_NONEPASS '0'
///只需要输入资金密码
#define JG_TDC_CONVERT_NEED_ONLYMONEYPASS '1'
///只需要输入银行密码
#define JG_TDC_CONVERT_NEED_ONLYBANKPASS '2'
///资金密码+银行密码
#define JG_TDC_CONVERT_NEED_MONEYANDBANKPASS '3'


typedef int TJGtdcServiceType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcServiceType 	业务类型 
/////////////////////////////////////////////////////////////////////////
///正常买入(委托)
#define  JG_TDC_SERVICE_TYPE_Buy					0
///正常卖出(委托
#define  JG_TDC_SERVICE_TYPE_Sell					1
///正常撤买(委托)
#define  JG_TDC_SERVICE_TYPE_CancelBuy				2
///正常撤卖(委托)
#define  JG_TDC_SERVICE_TYPE_CancelSell				3
///转托管(委托)
#define  JG_TDC_SERVICE_TYPE_Transfer				4
///撤转托管(委托)
#define  JG_TDC_SERVICE_TYPE_CancelTransfer			5
///配股认购(委托)
#define  JG_TDC_SERVICE_TYPE_Allotment				6
///配股认购撤单(委托)
#define  JG_TDC_SERVICE_TYPE_CancelAllotment		7
///转债转股（委托）
#define  JG_TDC_SERVICE_TYPE_ConvertDebt			8
///转债转股撤单（委托）
#define  JG_TDC_SERVICE_TYPE_CancelConvertDebt		9
///转债回售（委托）
#define  JG_TDC_SERVICE_TYPE_Resale					10
///转债回售撤单(委托)
#define  JG_TDC_SERVICE_TYPE_CancelResale			11
///新股申购
#define  JG_TDC_SERVICE_TYPE_NewStockApply			12
///新股申购自动撤单
#define  JG_TDC_SERVICE_TYPE_CancelNewStockApply	13
///新股认购或放弃
#define  JG_TDC_SERVICE_TYPE_NewStockSub			14
///新股认购撤单
#define  JG_TDC_SERVICE_TYPE_CancelNewStockSub		15
///指定交易
#define  JG_TDC_SERVICE_TYPE_09						16
///指定交易撤单
#define  JG_TDC_SERVICE_TYPE_19						17
///撤消指定
#define  JG_TDC_SERVICE_TYPE_0A						18
///撤消指定撤单
#define  JG_TDC_SERVICE_TYPE_1A						19
///回购指定
#define  JG_TDC_SERVICE_TYPE_0B 					20
///回购指定撤单
#define  JG_TDC_SERVICE_TYPE_1B						21
///回购撤消
#define  JG_TDC_SERVICE_TYPE_0C						22
///回购撤消撤单
#define  JG_TDC_SERVICE_TYPE_1C						23
///申购
#define  JG_TDC_SERVICE_TYPE_0D						24
///赎回
#define  JG_TDC_SERVICE_TYPE_0E						25
///申购撤单
#define  JG_TDC_SERVICE_TYPE_1D						26
///赎回撤单
#define  JG_TDC_SERVICE_TYPE_1E						27
///权证行权
#define  JG_TDC_SERVICE_TYPE_0J						28
///撤消行权
#define  JG_TDC_SERVICE_TYPE_1J						29
///预受要约
#define  JG_TDC_SERVICE_TYPE_0F						30
///预受要
#define  JG_TDC_SERVICE_TYPE_1F						31
///解除预受要约
#define  JG_TDC_SERVICE_TYPE_0G						32
///解除预受要约撤单
#define  JG_TDC_SERVICE_TYPE_1G						33
///ETF申购
#define  JG_TDC_SERVICE_TYPE_0M						34
///ETF赎回
#define  JG_TDC_SERVICE_TYPE_0N						35
///ETF申购撤单
#define  JG_TDC_SERVICE_TYPE_1M						36
///ETF赎回撤单
#define  JG_TDC_SERVICE_TYPE_1N						37
///股东代码同步-开户
#define  JG_TDC_SERVICE_TYPE_0H						38
///股东代码同步-销户
#define  JG_TDC_SERVICE_TYPE_1H						39
///无冻解质(国债出库)
#define  JG_TDC_SERVICE_TYPE_0I						40
///无冻解质撤单
#define  JG_TDC_SERVICE_TYPE_1I						41
///冻结解质
#define  JG_TDC_SERVICE_TYPE_0K						42
///冻结解质撤单
#define  JG_TDC_SERVICE_TYPE_1K						43
///无冻质押（债券入库）
#define  JG_TDC_SERVICE_TYPE_0L						44
///债券出库
#define  JG_TDC_SERVICE_TYPE_1L						45
///基金认购
#define  JG_TDC_SERVICE_TYPE_0O						46
///基金申购
#define  JG_TDC_SERVICE_TYPE_0P						47
///基金赎回
#define  JG_TDC_SERVICE_TYPE_0Q						48
///基金转换
#define  JG_TDC_SERVICE_TYPE_0R						49
///基金转换入
#define  JG_TDC_SERVICE_TYPE_0S						50
///基金转换出
#define  JG_TDC_SERVICE_TYPE_0T						51
///强制赎回
#define  JG_TDC_SERVICE_TYPE_0U						52
///红利发放
#define  JG_TDC_SERVICE_TYPE_0V						53
///开放式基金撤单
#define  JG_TDC_SERVICE_TYPE_0W						54
///分红方式设置
#define  JG_TDC_SERVICE_TYPE_0X						55
///场内基金赎回
#define  JG_TDC_SERVICE_TYPE_46						56
///场内基金强制赎
#define  JG_TDC_SERVICE_TYPE_4L						57
///场内基金申购
#define  JG_TDC_SERVICE_TYPE_45						58
///场内基金认购
#define  JG_TDC_SERVICE_TYPE_44						59
///场内开放基金合并
#define  JG_TDC_SERVICE_TYPE_0Y						60
///场内开放基金分拆
#define  JG_TDC_SERVICE_TYPE_0Z						61
///场内开放基金合并撤单
#define  JG_TDC_SERVICE_TYPE_1Y						62
///场内开放基金分拆撤单
#define  JG_TDC_SERVICE_TYPE_1Z						63
///黄金实物申购
#define  JG_TDC_SERVICE_TYPE_0a						64
///黄金实物赎回
#define  JG_TDC_SERVICE_TYPE_1b						65
///认购申请回执
#define  JG_TDC_SERVICE_TYPE_40						66
///申购申请回执
#define  JG_TDC_SERVICE_TYPE_41						67
///赎回申请回执
#define  JG_TDC_SERVICE_TYPE_42						68
///转托管转出申请回执
#define  JG_TDC_SERVICE_TYPE_43						69
///基金份额冻结确认
#define  JG_TDC_SERVICE_TYPE_4A						70
///基金份额解冻确认
#define  JG_TDC_SERVICE_TYPE_4B						71
///非交易过户转入确认
#define  JG_TDC_SERVICE_TYPE_4C						72
///非交易过户转出确认
#define  JG_TDC_SERVICE_TYPE_4D						73
///托管转出确认
#define  JG_TDC_SERVICE_TYPE_47						74
///设置分红方式确认
#define  JG_TDC_SERVICE_TYPE_48						75
///基金认购结果返款
#define  JG_TDC_SERVICE_TYPE_49						76
///转托管转入确认
#define  JG_TDC_SERVICE_TYPE_4E						77
///基金转换确认
#define  JG_TDC_SERVICE_TYPE_4F						78
///强行调增
#define  JG_TDC_SERVICE_TYPE_4G						79
///强行调减
#define  JG_TDC_SERVICE_TYPE_4H						80
///强行调增
#define  JG_TDC_SERVICE_TYPE_4J						81
///强行调减
#define  JG_TDC_SERVICE_TYPE_4K						82
///撤基金认购
#define  JG_TDC_SERVICE_TYPE_1O						83
///未知业务类型
#define  JG_TDC_SERVICE_TYPE_Unknown				999


typedef char TJGtdcETFType;
/////////////////////////////////////////////////////////////////////////
///TJGtdcETFType;			///< ETF属性
/////////////////////////////////////////////////////////////////////////
///单市场ETF
#define	JG_TDC_ETFTYPE_SingleMarket		1
///跨市场ETF
#define	JG_TDC_ETFTYPE_CrossMarket		2
///跨境ETF
#define	JG_TDC_ETFTYPE_CrossBorder		3
///实时申赎货币基金
#define	JG_TDC_ETFTYPE_IMF				4
///黄金ETF
#define	JG_TDC_ETFTYPE_Gold				5
///单市场实物债券ETF
#define	JG_TDC_ETFTYPE_Bond				6
///现金债券ETF
#define	JG_TDC_ETFTYPE_CashBond			7


typedef char TJGtdcConnectStates;
/////////////////////////////////////////////////////////////////////////
///TJGtdcConnectStates;			///< 网络连接状态
/////////////////////////////////////////////////////////////////////////
///未连接
#define	JG_TDC_NETYPE_Unconnected		1
///连接中
#define	JG_TDC_NETYPE_Connecting		2
///已连接
#define	JG_TDC_NETYPE_Connected			3
///连接失败
#define	JG_TDC_NETYPE_ConnectFailed		4


//////////////////////////////////////////////////////////////////////////
/// TJGtdcLocalIpType 内网IP
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcLocalIpType[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcNetIpType 外网IP
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcNetIpType[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMACType MAC地址
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcMACType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcHDSequenceNoType 硬盘序列号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcHDSequenceNoType[256];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCPUIDType CPUID
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcCPUIDType[128];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcVersion     Version
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcVersion[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcQSIDType     QSID
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcQSIDType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBranchNoType 营业部号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBranchNoType[8];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcLoginCodeType 用户名
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcLoginCodeType[39];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPasswordType 密码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcPassWordType[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMacAddressType
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcMacAddressType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcIPAddrType
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcIPAddrType[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMD5Type
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcMD5Type[64];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFieldItem 应答数据个数
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcFieldItem;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcClientIDType 客户号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcClientIDType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcReportNoType 申报号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcReportNoType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcClientNameType 客户姓名
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcClientNameType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundAccountType 资金账号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcFundAccountType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBankCodeType 银行代码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBankCodeType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBankNameType 银行名称
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBankNameType[64];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBankName 银行简称
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBankName[128];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBankAccountType 银行账号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBankAccountType[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBankAccount 银行账户
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBankAccount[64];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBankFlag 银行标识
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBankFlag[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStockAccountType 股东代码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcStockAccountType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcHolderNameType 股东姓名
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcHolderNameType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSeatNoType 席位号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcSeatNoType[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBatchNoType 批号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBatchNoType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStockCodeType 证券代码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcStockCodeType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStockNameType 证券名称
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcStockNameType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEntrustNoType 合同号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcEntrustNoType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcNewEntrustNoType 新合同号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcNewEntrustNoType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBusinessNoType 成交编号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBusinessNoType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcErrorInfoType 错误信息
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcErrorInfoType[128];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPositionStrType 定位串
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcPositionStrType[40];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOtherFundAccount 对方资金账号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcOtherFundAccountType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOtherStockAccount 对方股东代码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcOtherStockAccountType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOtherSeatNo 对方席位号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcOtherSeatNoType[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSettleInfo 账单信息
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcSettleInfoType[32 * 1024];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcContractCode 合约代码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcContractCodeType[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcContractName 合约名称
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcContractNameType[40];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFutureAccount 交易编码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcFutureAccountType[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRemarksInfo 备注信息
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcRemarksInfoType[128];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcContractNumber 合约编码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcContractNumber[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcInvalidReason 废单原因
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcInvalidReason[64];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBankPassword 银行密码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBankPassword[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundPassword 资金密码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcFundPassword[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcNoticeInfo 提示信息
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcNoticeInfo[256];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCheckPasswordFlag 校验密码标志
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcCheckPasswordFlag[128];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSettleInfo 账单信息
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcSettleInfo[32 * 1024];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcDigestName 业务名称
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcDigestName[32];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRemark 备注字段
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcRemark[128];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcInnerORG  内部机构
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcInnerORG[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAddMarginNotice  备注信息
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcAddMarginNotice[2050];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStrategyCode  策略代码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcStrategyCode[40];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchangeCombID  交易所组合编号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcExchangeCombID[40];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFullContractCode  完整合约代码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcFullContractCode[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCancelCustomer  撤单用户
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcCancelCustomer[16];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSerialNo  流水号
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcSerialNo[24];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOperationName  操作职工
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcOperationName[40];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcReviewName  复核职工
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcReviewName[40];



//////////////////////////////////////////////////////////////////////////
///  TJGtdcDate 日期
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcDate;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTime 时间
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcTime;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOrderVolume 委托数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOrderVolume;   
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOrderPrice 委托价格
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOrderPrice; 
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBuinessVolume 成交数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcBuinessVolume;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBusinessPrice 成交价格
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcBusinessPrice;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCancelVolume 撤单数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcCancelVolume;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBusinessBalance 成交金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcBusinessBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEnableBalance 可用余额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcEnableBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFetchBalance 可取余额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFetchBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFrozenBalance 冻结金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFrozenBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStockBalance 证券市值
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcStockBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundBalance 资金余额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFundBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAssetBalance 资产总值
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcAssetBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcIncome 总盈亏
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcIncome;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEnableBalanceHK 港股可用余额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcEnableBalanceHK;
/// TJGtdcStockAmount 股份余额
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcStockAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcYdAmount 昨日持仓量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcYdAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEnableAmount 可卖数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcEnableAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPurchaseAmount 可申购数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcPurchaseAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPossessAmount 当前拥股数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcPossessAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFrozenAmount 冻结数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcFrozenAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcYStoreAmount 昨日库存数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcYStoreAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCostPrice 成本价
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcCostPrice;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcKeepCostPrice 保本价
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcKeepCostPrice;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBuyCost  当前成本
/////////////////////////////////////////////////////////////////////////
typedef double TJGtdcBuyCost;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStockBalance 证券市值
/////////////////////////////////////////////////////////////////////////
typedef double TJGtdcStockBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFloatIncome 浮动盈亏
/////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFloatIncome;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcProIncome 累计盈亏
/////////////////////////////////////////////////////////////////////////
typedef double TJGtdcProIncome;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMortgageFlag 担保品标志
/////////////////////////////////////////////////////////////////////////
typedef char TJGtdcMortgageFlag;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcQueryAmount 查询数量
/////////////////////////////////////////////////////////////////////////
typedef double TJGtdcQueryAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxAmount 最大数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMaxAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPayBackBalance 还款金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPayBackBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMustBackAmount 应还数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMustBackAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCanBackAmount 可还数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcCanBackAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMustBackBalance 应还金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcMustBackBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCanBackBalance 可还金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcCanBackBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFinanceBailRatio 融资保证金比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFinanceBailRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcShortsellBailRatio 融券保证金比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcShortsellBailRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMortgageRatio 担保品折算率
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcMortgageRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxLoanAmount 可融券数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMaxLoanAmount;
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEnableBail 可用保证金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcEnableBail;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCreditQuota 授信额度
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcCreditQuota;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFinanceQuota 可融资金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFinanceQuota;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcShortsellQuota 可融券额度
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcShortsellQuota;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAssureRatio 维持担保比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcAssureRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTotalDebit 总负债
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcTotalDebit;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundDebit 资金负债
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFundDebit;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStockDebit 股票负债
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcStockDebit;
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMustBackBalance 应还金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcMustBackBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPayBackBalance 还款金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPayBackBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMustBackAmount 应还数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMustBackAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCanBackAmount 可还数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcCanBackAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCanBackBalance 可还金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcCanBackBalance;
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOccurAmount 发生数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOccurAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOccurBalance 发生金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcOccurBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBackAmount 归还数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcBackAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBackBalance 归还金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcBackBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSettleInfoLen 账单信息长度
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcSettleInfoLen;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAmountMultiple 合约乘数
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcAmountMultiple;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSurplusAmount 剩余数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcSurplusAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPriceTick 最小价位变动
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPriceTick;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxMarketEntrustAmount 市价单最大下单量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMaxMarketEntrustAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxLimitEntrustAmount 限价单最大下单量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMaxLimitEntrustAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBuyBailRatio 多头保证金比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcBuyBailRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSellBailRatio 空头保证金比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcSellBailRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStaticRightsBalance 静态权益
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcStaticRightsBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcDynamicRightsBalance 动态权益
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcDynamicRightsBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPossessBail 占用保证金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPossessBail;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcHoldIncome 持仓盈亏
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcHoldIncome;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPayoffIncome 平仓盈亏
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPayoffIncome;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcYdAmount 昨日持仓量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcYdAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTodayAmount 今日持仓量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcTodayAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOpenAmount 开仓数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOpenAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPayoffAmount 平仓数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcPayoffAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcHoldCost 持仓成本
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcHoldCost;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOptionVersion 期权版本
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcOptionVersion;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOptionPreClosePrice 期权前收盘价格
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOptionPreClosePrice;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStockPreClosePrice 证券前收盘价格
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcStockPreClosePrice;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOptionUpPrice 期权涨停价格
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOptionUpPrice;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOptionDownPrice 期权跌停价格
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOptionDownPrice;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExerciseAmount 行权数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcExerciseAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExercisePrice 行权价格
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcExercisePrice;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcUnitBail 单位保证金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcUnitBail;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxMarketEntrustAmount 市价单最大下单量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMaxMarketEntrustAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMinMarketEntrustAmount 市价单最小下单量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMinMarketEntrustAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxLimitEntrustAmount 限价单最大下单量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMaxLimitEntrustAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMinLimitEntrustAmount 限价单最小下单量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMinLimitEntrustAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcUsedBail 已用保证金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcUsedBail;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAgreeAssureRatio 履约担保比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcAgreeAssureRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRiskRatio 风险度
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcRiskRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRiskRatio1 风险度1
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcRiskRatio1;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOptionYDAmount 期权昨日余额
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOptionYDAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOptionAmount 期权余额
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcOptionAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcUnFrozenAmount 解冻数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcUnFrozenAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTransitAmount 在途数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcTransitAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTodayOpenAmount 今日开仓量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcTodayOpenAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTodayPayoffAmount 今日平仓量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcTodayPayoffAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPremiumBalance 权利金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPremiumBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBailBalance 保证金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcBailBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOptionBalance 期权市值
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcOptionBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSettleAmount 交收数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcSettleAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSettleBalance 结算金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcSettleBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcNeedAmount 不足数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcNeedAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcNeedBalance 不足金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcNeedBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCoveredLockAmount 备兑锁定数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcCoveredLockAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCoveredLackAmount 备兑不足数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcCoveredLackAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCoveredPreLackAmount 备兑预估不足数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcCoveredPreLackAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExerciseStrategyValue 行权策略值
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExerciseStrategyValue;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAgreeAmount 协议数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcAgreeAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBankBalance 银行余额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcBankBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTransBalance 转账金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcTransBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAmount 数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdciAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSettleInfoUseLen 账单信息使用长度
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcSettleInfoUseLen;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcClearBalance 清算金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcClearBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBusinessCount 成交笔数
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcBusinessCount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFeeStamp 印花税
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFeeStamp;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFeeCommission 佣金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFeeCommission;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFeeTransfer 过户费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFeeTransfer;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFare3 费用3
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFare3;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFareX 费用x
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFareX;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFeeOther 其它费用
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFeeOther;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcStandardFare0 标准佣金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcStandardFare0;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchange_AllFee 一级总费用
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExchange_AllFee;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchange_Fee0 一级经手费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExchange_Fee0;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchange_Stamp 一级印花税
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExchange_Stamp;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchange_Transfer 一级过户费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExchange_Transfer;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchange_SEC 一级证管费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExchange_SEC;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchange_Fee 一级手续费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExchange_Fee;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchange_FareX 风险金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExchange_FareX;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundPreBLN 期初结存
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFundPreBLN;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundBLN 期末结存
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFundBLN;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundAvl 可用资金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFundAvl;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundExeFRZ 行权资金冻结金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFundExeFRZ;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFundExeMargin 行权冻结维持保证金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFundExeMargin;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPaylater 垫付资金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPaylater;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExpectPnint 预计垫资罚息
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExpectPnint;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRepayPaylater 归还垫资
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcRepayPaylater;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRepayPnint 归还罚息
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcRepayPnint;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRemitPnint 减免罚息
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcRemitPnint;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcInterstIncome 利息归本
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcInterstIncome;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcInterstIncomeTax 利息税
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcInterstIncomeTax;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcInAmt 银衍入金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcInAmt;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOutAmt 银衍出金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcOutAmt;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPremiumPay 权利金收付
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPremiumPay;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExercisePay 行权收付
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExercisePay;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcClearingFee 结算费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcClearingFee;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExchangeFee 交易所经手费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExchangeFee;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExerciseClearingFee 行权结算费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExerciseClearingFee;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcExerciseFee 行权手续费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcExerciseFee;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcQuotaValUsed 占用买入额度
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcQuotaValUsed;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcQuotaVal 买入额度
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcQuotaVal;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRightMktVal 权利仓市值
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcRightMktVal;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcDuteMktVal 义务仓市值
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcDuteMktVal;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMarginRiskRate 保证金风险率
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcMarginRiskRate;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcAddMarginAmt 应追加保证金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcAddMarginAmt;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcConfirmFlag 客户确认标志
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcConfirmFlag;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEntrustLots 委托手数
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcEntrustLots;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEntrustType 委托类型
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcEntrustType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFrozenBail 冻结保证金
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFrozenBail;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFrozenFee 冻结手续费
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFrozenFee;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCombAmount 组合持仓量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcCombAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcDistanceAutoSplitDates 距离自动拆分天数
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcDistanceAutoSplitDates;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBSComb 组合买卖方向
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcBSComb;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxCombAmount 最大组合数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMaxCombAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxSplitAmount 最大拆分数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcMaxSplitAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcRestBalance 资金余额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcRestBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOpConfigInfo 操作确认信息
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcOpConfigInfo[64];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOpRetCode 操作返回代码
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcOpRetCode;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcOperBalance 操作金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcOperBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTransBalance 内转金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcTransBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcFinanceBailRatio 融资保证金比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcFinanceBailRatio;				
//////////////////////////////////////////////////////////////////////////
/// TJGtdcShortsellBailRatio 融券保证金比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcShortsellBailRatio;				
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMortgageRatio 担保品折算率
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcMortgageRatio;		
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCashSubstituteFlag 现金替代标志
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcCashSubstituteFlag;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcPremiumRatio 溢价比例
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcPremiumRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCashSubstituteAmount 现金替代金额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcCashSubstituteAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcApplyRedeemUnit 最小申购赎回份数
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcApplyRedeemUnit;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEstimateCashBalance 当日日预估现金余额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcEstimateCashBalance;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcMaxCashRatio 现金替代比例上限
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcMaxCashRatio;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEstimateCashComponent 上日预估现金差额
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcEstimateCashComponent;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcSampleAmount 成分股数量
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcSampleAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTotalAmount 篮子股总数量
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcTotalAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcTNAV 资产净值
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcTNAV;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcApplyRedeem 是否允许进行申购赎回
//////////////////////////////////////////////////////////////////////////
typedef bool TJGtdcApplyRedeem;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCertCode 证件号码
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcCertCode[50];
//////////////////////////////////////////////////////////////////////////
/// TJGtdcCertType 证件类型
//////////////////////////////////////////////////////////////////////////
typedef int TJGtdcCertType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcNetPortType  端口
//////////////////////////////////////////////////////////////////////////
typedef unsigned short TJGtdcNetPortType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcNetBytes  字节数
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcNetBytes;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcNetDomain  是否为域名
//////////////////////////////////////////////////////////////////////////
typedef bool TJGtdcNetDomain;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcDPriceType  价格
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcDPriceType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcBalanceType  额度
//////////////////////////////////////////////////////////////////////////
typedef double TJGtdcBalanceType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcEdType  额度状态
//////////////////////////////////////////////////////////////////////////
typedef char TJGtdcEdType;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcReserveAmount  保底数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcReserveAmount;
//////////////////////////////////////////////////////////////////////////
/// TJGtdcReserveRemainAmount  保底剩余数量
//////////////////////////////////////////////////////////////////////////
typedef __int64 TJGtdcReserveRemainAmount;
#endif /*  JG_TDCDATATYPE_H */