# Tx 网格交易策略运行和回调指南

## 概述
网格策略（grid.q）是Tx平台中的一个重要交易策略模块，实现了自动化的网格交易逻辑。该策略通过在价格网格中设置买卖订单，实现低买高卖的交易策略。

## 网格策略核心机制

### 主要回调函数
网格策略定义了以下核心回调函数：

1. **ont_grid** - 定时器回调（每秒触发）
2. **onq_grid** - 行情数据回调
3. **ono_grid** - 订单状态变化回调
4. **onr_grid** - 日切回调
5. **oni_grid** - 系统初始化回调
6. **one_grid** - 系统退出回调
7. **ond_grid** - 日切回调

### 策略配置结构
网格策略使用 `.db.Ts[策略ID;`TP]` 表存储策略参数：

```q
// 主要配置参数
sym          // 交易合约
stop         // 停止标志
logpx        // 是否使用对数价格
pxbase       // 基准价格
qtybase      // 基准持仓
nup          // 向上次数
ndn          // 向下次数
stepup       // 向上步长
stepdn       // 向下步长
qtyups       // 向上数量增量
qtydnl       // 向下数量增量
posinf       // 持仓下限
possup       // 持仓上限
spreadmax    // 最大价差
closelongrange   // 多头止损区间
closeshortrange  // 空头止损区间
oidup        // 当前向上订单ID
oiddn        // 当前向下订单ID
```

## 运行网格策略的步骤

### 1. 配置策略参数

创建策略配置文件（如 `cftsqtx.q`）：

```q
\d .db

// 激活策略
Ts.qtx.active:1b;
Ts.qtx.acc:`ctp;              // 交易账户
Ts.qtx.accx:`symbol$();       // 扩展账户
Ts.qtx.stop:0b;               // 停止标志

// 设置回调函数
Ts.qtx.event.timer:`ont_grid;     // 定时器回调
Ts.qtx.event.quote:`onq_grid;     // 行情回调
Ts.qtx.event.exerpt:`ono_grid;    // 订单回调
Ts.qtx.event.sysinit:`oni_grid;   // 初始化回调
Ts.qtx.event.sysexit:`one_grid;   // 退出回调
Ts.qtx.event.dayroll:`ond_grid;   // 日切回调

// 策略参数
Ts.qtx.mode:`MANUAL;              // 手动模式
Ts.qtx.xsym:`IF1905.CCFX;        // 交易合约
Ts.qtx.TRDTIME:enlist 00:00 24:00; // 交易时间
Ts.qtx.RATIO:0.0125;             // 比例参数
Ts.qtx.ANCHORPX:1217.657;        // 锚定价格
Ts.qtx.ANCHORUP:1232.878;        // 向上锚点
Ts.qtx.ANCHORDN:1202.436;        // 向下锚点
Ts.qtx.POSSUP:-60f;              // 持仓上限
Ts.qtx.POSINF:-64f;              // 持仓下限
Ts.qtx.GRIDSIZE:1f;              // 网格大小
Ts.qtx.SPREADMAX:0.5f;           // 最大价差
Ts.qtx.SHORTSTOPRANGE:1308 1313f; // 空头止损区间
Ts.qtx.LONGSTOPRANGE:1095 1100f;  // 多头止损区间

\d .
```

### 2. 启动系统

使用启动脚本启动Tx系统：

```bash
# 启动期货交易系统
./startfcqtx
```

或者手动启动：

```bash
cd /kdb && q Tx/core/base.q -conf qtx/cffc -code 'txload each ("core/fcbase";"ui/uibase";"tsl/grid")' -p 127.0.0.1:5000
```

### 3. 加载策略

在kdb+控制台中加载网格策略：

```q
// 加载网格策略模块
txload "tsl/grid"

// 加载策略配置
\l conf/qtx/ts/cftsqtx.q

// 或者直接配置策略参数
.db.Ts.qtx.TP,:(`IF1905.CCFX;0b;0b;3800f;1f;0;0;0n;0n;0n;0n;0n;0n;5f;5f;1f;1f;-10f;10f;()!();()!();()!();()!();2f;3750 3780f;3820 3850f;();());
```

## 回调函数详解

### 1. 定时器回调 (ont_grid)

每秒触发一次，检查交易时间和策略状态：

```q
ont_grid:{[x;y]
    t:`time$y;  // 当前时间
    // 对每个活跃且未停止的合约执行网格检查
    grid_check[x] each exec sym from .db.Ts[x;`TP] where istrading[t] each sym,not stop;
};
```

**触发条件：**
- 系统定时器（通常每秒）
- 策略处于活跃状态
- 在交易时间内

### 2. 行情回调 (onq_grid)

行情数据更新时触发：

```q
onq_grid:{[x;y];};  // 当前版本为空实现
```

**触发条件：**
- 订阅的合约行情数据更新
- 买卖价格发生变化

### 3. 订单回调 (ono_grid)

订单状态变化时触发：

```q
ono_grid:{[x;y]
    // 处理订单状态变化
    // 更新策略状态
    // 检查是否需要新的网格订单
    if[not .db.Ts[x;`TP;s;`stop];grid_check[x;s]];
};
```

**触发条件：**
- 订单成交
- 订单取消
- 订单拒绝

### 4. 核心网格检查函数 (grid_check)

这是网格策略的核心逻辑：

```q
grid_check:{[x;s]
    // 获取策略参数和市场数据
    r:.db.Ts[x;`TP;s];
    h:.db.QX[s];
    
    // 计算中间价和价差
    p:(0.5*sum h`bid`ask)^h`price;
    sp:h[`ask]-h[`bid];
    
    // 获取当前净持仓
    q0:netpos[x;s];
    
    // 检查止损条件
    if[触发止损条件;
        // 取消所有挂单
        // 平仓
        // 设置停止标志
    ];
    
    // 计算网格价格
    // 检查是否需要挂新单
    // 管理现有订单
};
```

## 策略运行监控

### 1. 查看策略状态

```q
// 查看策略配置
.db.Ts.qtx

// 查看策略持仓表
select from .db.Ts.qtx.TP

// 查看当前订单
select from .db.O where ts=`qtx,not end

// 查看持仓情况
select from .db.P where ts=`qtx
```

### 2. 手动控制策略

```q
// 停止策略
.db.Ts.qtx.stop:1b;

// 重启策略
.db.Ts.qtx.stop:0b;

// 修改策略参数
.db.Ts.qtx.TP[`IF1905.CCFX;`spreadmax]:1.0f;

// 手动触发网格检查
grid_check[`qtx;`IF1905.CCFX];
```

### 3. 风险控制

```q
// 设置止损区间
.db.Ts.qtx.TP[`IF1905.CCFX;`closelongrange]:3750 3780f;
.db.Ts.qtx.TP[`IF1905.CCFX;`closeshortrange]:3820 3850f;

// 设置持仓限制
.db.Ts.qtx.TP[`IF1905.CCFX;`posinf]:-10f;  // 最大空头持仓
.db.Ts.qtx.TP[`IF1905.CCFX;`possup]:10f;   // 最大多头持仓
```

## 常见问题和解决方案

### 1. 策略不执行
- 检查 `active` 标志是否为 `1b`
- 确认在交易时间内
- 检查 `stop` 标志是否为 `0b`

### 2. 订单不成交
- 检查价格是否在合理范围内
- 确认账户资金充足
- 检查合约是否可交易

### 3. 回调函数不触发
- 确认事件订阅配置正确
- 检查策略ID是否匹配
- 验证回调函数名称拼写

### 4. 调试技巧

```q
// 开启调试模式
.debug.grid:1b;

// 查看最近的日志
select from .db.LOG where sym like "*grid*"

// 手动测试回调函数
ont_grid[`qtx;.z.P];
```

## 总结

网格策略通过事件驱动的回调机制实现自动化交易。关键是正确配置策略参数，理解各个回调函数的触发条件，并做好风险控制。在实际使用中，建议先在模拟环境中测试策略逻辑，确认无误后再投入实盘交易。
